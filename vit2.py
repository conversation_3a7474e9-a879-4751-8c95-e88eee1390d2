"""
Vision Transformer (ViT) with Optical Flow for Video Classification

This implementation uses optical flow techniques instead of processing all video frames:
- Computes optical flow between consecutive frames using Farneback method
- Combines original frames with optical flow magnitude for richer motion information
- Reduces computational load by focusing on motion patterns rather than all frames
- Supports different optical flow methods: Farneback, Lucas-Kanade, TV-L1
- Supports different data combination methods: concatenate, magnitude_only, separate

Key improvements over frame-based approach:
1. Motion-aware: Captures temporal dynamics and movement patterns
2. Efficient: Processes fewer data points while retaining motion information
3. Robust: Less sensitive to static background elements
4. Flexible: Multiple optical flow algorithms and combination strategies
"""

import os
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
from helper import (
    load_all_class_frames_with_optical_flow,
    create_optical_flow_dataset,
    evaluateModel,
    saveBestModel,
    saveLastModel
)
import matplotlib.pyplot as plt
import pandas as pd
from config import (
    VIT_RESULTS_DIR,
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    NUM_FRAMES,
    MAX_FRAMES,
    EPOCHS,
    BATCH_SIZE,
    OPTICAL_FLOW_FARNEBACK,
    FLOW_CONCATENATE,
    FLOW_MAGNITUDE_ONLY,
    FLOW_SEPARATE,
)
from frames import group_frames_into_clips

# Optical Flow Configuration
OPTICAL_FLOW_METHOD = OPTICAL_FLOW_FARNEBACK  # Options: farneback, lucas_kanade, tvl1
FLOW_DATASET_METHOD = FLOW_CONCATENATE  # Options: concatenate, separate, magnitude_only, features_only


class PatchEmbedding(layers.Layer):
    def __init__(self, patch_size=16, embed_dim=768):
        super(PatchEmbedding, self).__init__()
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = (IMG_SIZE // patch_size) ** 2
        self.proj = layers.Conv2D(
            filters=embed_dim,
            kernel_size=patch_size,
            strides=patch_size,
            padding="valid",
        )
        # Initialize pos_embed and cls_token with correct add_weight syntax
        self.pos_embed = self.add_weight(
            name="pos_embed",
            shape=(1, NUM_FRAMES * self.num_patches + 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )
        self.cls_token = self.add_weight(
            name="cls_token",
            shape=(1, 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )

    def call(self, x):
        # x: (batch_size, NUM_FRAMES, IMG_SIZE, IMG_SIZE, channels)
        batch_size = tf.shape(x)[0]
        frames = tf.split(
            x, NUM_FRAMES, axis=1
        )  # List of (batch_size, 1, IMG_SIZE, IMG_SIZE, channels)
        frames = [
            tf.squeeze(frame, axis=1) for frame in frames
        ]  # (batch_size, IMG_SIZE, IMG_SIZE, channels)

        # Extract patches for each frame
        patches = []
        for frame in frames:
            frame_patches = self.proj(frame)  # (batch_size, h, w, embed_dim)
            frame_patches = tf.reshape(
                frame_patches, [batch_size, self.num_patches, self.embed_dim]
            )
            patches.append(frame_patches)

        # Concatenate patches across frames
        x = tf.concat(
            patches, axis=1
        )  # (batch_size, NUM_FRAMES * num_patches, embed_dim)

        # Add CLS token
        cls_tokens = tf.tile(
            self.cls_token, [batch_size, 1, 1]
        )  # (batch_size, 1, embed_dim)
        x = tf.concat(
            [cls_tokens, x], axis=1
        )  # (batch_size, NUM_FRAMES * num_patches + 1, embed_dim)

        # Add positional embedding
        x = x + self.pos_embed
        return x


class TransformerEncoder(layers.Layer):
    def __init__(self, embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.norm1 = layers.LayerNormalization(epsilon=1e-6)
        self.attn = layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )
        self.norm2 = layers.LayerNormalization(epsilon=1e-6)
        self.mlp = models.Sequential(
            [
                layers.Dense(mlp_dim, activation="gelu"),
                layers.Dropout(dropout),
                layers.Dense(embed_dim),
                layers.Dropout(dropout),
            ]
        )

    def call(self, x, training=False):
        x = x + self.attn(self.norm1(x), self.norm1(x), training=training)
        x = x + self.mlp(self.norm2(x), training=training)
        return x


def build_classifier(input_channels=2):
    """
    Build Vision Transformer classifier for optical flow data.

    Args:
        input_channels: Number of input channels (2 for optical flow with magnitude)
    """
    inputs = layers.Input(shape=(NUM_FRAMES, IMG_SIZE, IMG_SIZE, input_channels))

    # Patch embedding
    x = PatchEmbedding(patch_size=16, embed_dim=768)(inputs)

    # Transformer encoder layers
    for _ in range(4):  # Reduced depth for computational efficiency
        x = TransformerEncoder(embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1)(
            x, training=True
        )

    # Final classification
    x = layers.LayerNormalization(epsilon=1e-6)(x)
    cls_token = x[:, 0]  # Extract CLS token
    outputs = layers.Dense(len(CATEGORIES), activation="softmax")(cls_token)

    model = models.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# Load video data with optical flow
print("Loading data with optical flow...")
clf_frames, clf_flows, clf_flow_features, clf_labels = load_all_class_frames_with_optical_flow(
    max_frames=MAX_FRAMES,
    CATEGORIES=CATEGORIES,
    DATA_PATH=DATA_PATH,
    skip=SKIP,
    flow_method=OPTICAL_FLOW_METHOD
)

# Create optical flow dataset
print(f"Creating optical flow dataset using method: {FLOW_DATASET_METHOD}")
clf_data = create_optical_flow_dataset(
    clf_frames, clf_flows, clf_flow_features, method=FLOW_DATASET_METHOD
)

# Debug: Print data shapes
print(f"clf_frames shape: {clf_frames.shape}")
print(f"clf_flows shape: {clf_flows.shape}")
print(f"clf_flow_features shape: {clf_flow_features.shape}")
print(f"clf_data shape (frames + flow): {clf_data.shape}")
print(f"clf_labels shape: {clf_labels.shape}")

# Group frames into clips
clf_data, clf_labels = group_frames_into_clips(clf_data, clf_labels, NUM_FRAMES)
print(f"Clipped clf_data shape: {clf_data.shape}")
print(f"Clipped clf_labels shape: {clf_labels.shape}")

# Split data
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)
print(f"x_clf_train shape: {x_clf_train.shape}")
print(f"x_clf_test shape: {x_clf_test.shape}")

# Determine input channels based on flow dataset method
if FLOW_DATASET_METHOD == FLOW_CONCATENATE:
    input_channels = 2  # frame + optical flow magnitude
elif FLOW_DATASET_METHOD == FLOW_MAGNITUDE_ONLY:
    input_channels = 1  # only optical flow magnitude
elif FLOW_DATASET_METHOD == FLOW_SEPARATE:
    input_channels = 1  # frames only (flows handled separately)
else:
    input_channels = 1  # default

print(f"Building classifier with {input_channels} input channels for method: {FLOW_DATASET_METHOD}")
classifier = build_classifier(input_channels=input_channels)
print(classifier.summary())

# Ensure results dir exists and prepare checkpoint callback to save each epoch
os.makedirs(VIT_RESULTS_DIR, exist_ok=True)

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=EPOCHS,
    batch_size=BATCH_SIZE,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{VIT_RESULTS_DIR}/best_model_vit.h5"),
        saveLastModel(f"{VIT_RESULTS_DIR}/last_model_vit.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, CATEGORIES, VIT_RESULTS_DIR)
