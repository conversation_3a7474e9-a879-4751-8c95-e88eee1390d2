import os
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
from helper import (
    create_optical_flow_dataset,
    evaluateModel,
    saveBestModel,
    saveLastModel
)
from frames import advanced_preprocess_frame
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from config import (
    VIT_RESULTS_DIR,
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    NUM_FRAMES,
    MAX_FRAMES,
    EPOCHS,
    BATCH_SIZE,
    OPTICAL_FLOW_FARNEBACK,
    OPTICAL_FLOW_LUCAS_KANADE,
    OPTICAL_FLOW_TVL1,
    FLOW_CONCATENATE,
    FLOW_MAGNITUDE_ONLY,
    FLOW_SEPARATE,
    FLOW_FEATURES_ONLY,
    ADVANCED_PROCESSING,
)
from frames import group_frames_into_clips

# Optical Flow Configuration - Using advanced techniques from helper.py
OPTICAL_FLOW_METHOD = OPTICAL_FLOW_FARNEBACK  # farneback, lucas_kanade, tvl1
FLOW_DATASET_METHOD = FLOW_CONCATENATE  # concatenate, separate, magnitude_only, features_only

# Preprocessing Configuration - Using advanced techniques from frames.py
PREPROCESSING_TECHNIQUES = ADVANCED_PROCESSING  # Uses advanced preprocessing from config


def compute_optical_flow_farneback(frame1, frame2):
    """Compute dense optical flow using Farneback method."""
    flow = cv2.calcOpticalFlowPyrLK(frame1, frame2, None,
                                   pyr_scale=0.5, levels=3, winsize=15,
                                   iterations=3, poly_n=5, poly_sigma=1.2, flags=0)
    return flow


def optical_flow_magnitude(flow):
    """Calculate magnitude of optical flow vectors."""
    return np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)


def extract_optical_flow_features(flow):
    """Extract statistical features from optical flow."""
    magnitude = optical_flow_magnitude(flow)
    features = {
        'mean_magnitude': np.mean(magnitude),
        'std_magnitude': np.std(magnitude),
        'max_magnitude': np.max(magnitude),
        'min_magnitude': np.min(magnitude),
        'mean_flow_x': np.mean(flow[..., 0]),
        'mean_flow_y': np.mean(flow[..., 1]),
        'std_flow_x': np.std(flow[..., 0]),
        'std_flow_y': np.std(flow[..., 1]),
        'flow_density': np.sum(magnitude > np.mean(magnitude)) / magnitude.size
    }
    return features


def load_optical_flow_data_with_advanced_preprocessing(max_frames=5, skip=20):
    """
    Load video data with optical flow using advanced preprocessing techniques.
    This function avoids loading all frames by using optical flow between consecutive frames.
    """
    all_data = []
    all_flows = []
    all_flow_features = []
    all_labels = []
    label_map = {cat: idx for idx, cat in enumerate(CATEGORIES)}

    for cat in CATEGORIES:
        print(f"Processing {cat} with optical flow and advanced preprocessing...")
        folder = os.path.join(DATA_PATH, cat)

        if not os.path.exists(folder):
            print(f"Folder not found: {folder}")
            continue

        files = [f for f in os.listdir(folder) if f.lower().endswith((".mp4", ".avi", ".mov", ".mkv"))]
        print(f"Found {len(files)} video files in {folder}")

        for file in files:
            cap = cv2.VideoCapture(os.path.join(folder, file))
            count = 0
            frame_idx = 0
            prev_frame = None

            while cap.isOpened() and count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break

                # Skip frames according to skip parameter
                if frame_idx % (skip + 1) == 0:
                    # Apply advanced preprocessing
                    processed = advanced_preprocess_frame(frame, IMG_SIZE, PREPROCESSING_TECHNIQUES)
                    if processed is None:
                        frame_idx += 1
                        continue

                    # Convert to uint8 for optical flow computation
                    current_frame = (processed * 255).astype(np.uint8) if processed.dtype != np.uint8 else processed

                    if prev_frame is not None:
                        # Compute optical flow using Farneback method
                        flow = compute_optical_flow_farneback(prev_frame, current_frame)

                        # Extract flow features
                        features = extract_optical_flow_features(flow)

                        # Store data
                        all_data.append(np.expand_dims(processed, -1))
                        all_flows.append(flow)
                        all_flow_features.append(list(features.values()))
                        all_labels.append(label_map[cat])
                        count += 1

                    prev_frame = current_frame.copy()

                frame_idx += 1

            cap.release()

        print(f"Extracted {len([l for l in all_labels if l == label_map[cat]])} samples from {cat}")

    print(f"Total samples extracted: {len(all_data)}")
    return (np.array(all_data), np.array(all_flows),
            np.array(all_flow_features), np.array(all_labels))


class PatchEmbedding(layers.Layer):
    def __init__(self, patch_size=16, embed_dim=768):
        super(PatchEmbedding, self).__init__()
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = (IMG_SIZE // patch_size) ** 2
        self.proj = layers.Conv2D(
            filters=embed_dim,
            kernel_size=patch_size,
            strides=patch_size,
            padding="valid",
        )
        # Initialize pos_embed and cls_token with correct add_weight syntax
        self.pos_embed = self.add_weight(
            name="pos_embed",
            shape=(1, NUM_FRAMES * self.num_patches + 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )
        self.cls_token = self.add_weight(
            name="cls_token",
            shape=(1, 1, embed_dim),
            initializer="random_normal",
            trainable=True,
        )

    def call(self, x):
        # x: (batch_size, NUM_FRAMES, IMG_SIZE, IMG_SIZE, channels)
        batch_size = tf.shape(x)[0]
        frames = tf.split(
            x, NUM_FRAMES, axis=1
        )  # List of (batch_size, 1, IMG_SIZE, IMG_SIZE, channels)
        frames = [
            tf.squeeze(frame, axis=1) for frame in frames
        ]  # (batch_size, IMG_SIZE, IMG_SIZE, channels)

        # Extract patches for each frame
        patches = []
        for frame in frames:
            frame_patches = self.proj(frame)  # (batch_size, h, w, embed_dim)
            frame_patches = tf.reshape(
                frame_patches, [batch_size, self.num_patches, self.embed_dim]
            )
            patches.append(frame_patches)

        # Concatenate patches across frames
        x = tf.concat(
            patches, axis=1
        )  # (batch_size, NUM_FRAMES * num_patches, embed_dim)

        # Add CLS token
        cls_tokens = tf.tile(
            self.cls_token, [batch_size, 1, 1]
        )  # (batch_size, 1, embed_dim)
        x = tf.concat(
            [cls_tokens, x], axis=1
        )  # (batch_size, NUM_FRAMES * num_patches + 1, embed_dim)

        # Add positional embedding
        x = x + self.pos_embed
        return x


class TransformerEncoder(layers.Layer):
    def __init__(self, embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.norm1 = layers.LayerNormalization(epsilon=1e-6)
        self.attn = layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )
        self.norm2 = layers.LayerNormalization(epsilon=1e-6)
        self.mlp = models.Sequential(
            [
                layers.Dense(mlp_dim, activation="gelu"),
                layers.Dropout(dropout),
                layers.Dense(embed_dim),
                layers.Dropout(dropout),
            ]
        )

    def call(self, x, training=False):
        x = x + self.attn(self.norm1(x), self.norm1(x), training=training)
        x = x + self.mlp(self.norm2(x), training=training)
        return x


def build_classifier(input_channels=2):
    """
    Build Vision Transformer classifier for optical flow data.

    Args:
        input_channels: Number of input channels (2 for optical flow with magnitude)
    """
    inputs = layers.Input(shape=(NUM_FRAMES, IMG_SIZE, IMG_SIZE, input_channels))

    # Patch embedding
    x = PatchEmbedding(patch_size=16, embed_dim=768)(inputs)

    # Transformer encoder layers
    for _ in range(4):  # Reduced depth for computational efficiency
        x = TransformerEncoder(embed_dim=768, num_heads=8, mlp_dim=3072, dropout=0.1)(
            x, training=True
        )

    # Final classification
    x = layers.LayerNormalization(epsilon=1e-6)(x)
    cls_token = x[:, 0]  # Extract CLS token
    outputs = layers.Dense(len(CATEGORIES), activation="softmax")(cls_token)

    model = models.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# Load video data with optical flow and advanced preprocessing
print("Loading data with optical flow and advanced preprocessing...")
print(f"Using optical flow method: {OPTICAL_FLOW_METHOD}")
print(f"Using preprocessing techniques: {PREPROCESSING_TECHNIQUES}")
clf_frames, clf_flows, clf_flow_features, clf_labels = load_optical_flow_data_with_advanced_preprocessing(
    max_frames=MAX_FRAMES,
    skip=SKIP
)

# Create optical flow dataset
print(f"Creating optical flow dataset using method: {FLOW_DATASET_METHOD}")
clf_data = create_optical_flow_dataset(
    clf_frames, clf_flows, clf_flow_features, method=FLOW_DATASET_METHOD
)

# Debug: Print data shapes
print(f"clf_frames shape: {clf_frames.shape}")
print(f"clf_flows shape: {clf_flows.shape}")
print(f"clf_flow_features shape: {clf_flow_features.shape}")
print(f"clf_data shape (frames + flow): {clf_data.shape}")
print(f"clf_labels shape: {clf_labels.shape}")

# Group frames into clips
clf_data, clf_labels = group_frames_into_clips(clf_data, clf_labels, NUM_FRAMES)
print(f"Clipped clf_data shape: {clf_data.shape}")
print(f"Clipped clf_labels shape: {clf_labels.shape}")

# Split data
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)
print(f"x_clf_train shape: {x_clf_train.shape}")
print(f"x_clf_test shape: {x_clf_test.shape}")

# Determine input channels based on flow dataset method
if FLOW_DATASET_METHOD == FLOW_CONCATENATE:
    input_channels = 2  # frame + optical flow magnitude
elif FLOW_DATASET_METHOD == FLOW_MAGNITUDE_ONLY:
    input_channels = 1  # only optical flow magnitude
elif FLOW_DATASET_METHOD == FLOW_SEPARATE:
    input_channels = 1  # frames only (flows handled separately)
else:
    input_channels = 1  # default

print(f"Building classifier with {input_channels} input channels for method: {FLOW_DATASET_METHOD}")
classifier = build_classifier(input_channels=input_channels)
print(classifier.summary())

# Ensure results dir exists and prepare checkpoint callback to save each epoch
os.makedirs(VIT_RESULTS_DIR, exist_ok=True)

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=EPOCHS,
    batch_size=BATCH_SIZE,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{VIT_RESULTS_DIR}/best_model_vit.h5"),
        saveLastModel(f"{VIT_RESULTS_DIR}/last_model_vit.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, CATEGORIES, VIT_RESULTS_DIR)
