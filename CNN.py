import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import matplotlib.pyplot as plt
import pandas as pd
from helper import load_all_class_frames, evaluateModel, saveBestModel, saveLastModel
from config import (
    CNN_RESULTS_DIR,
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    EPOCHS,
    BATCH_SIZE,
    MAX_FRAMES,
)


def build_classifier():
    model = models.Sequential(
        [
            layers.Input(shape=(IMG_SIZE, IMG_SIZE, 1)),
            layers.Conv2D(32, 3, activation="relu", padding="same"),
            layers.MaxPooling2D(),
            layers.Conv2D(64, 3, activation="relu", padding="same"),
            layers.MaxPooling2D(),
            layers.Flatten(),
            layers.Dense(128, activation="relu"),
            layers.Dropout(0.3),
            layers.Dense(len(CATEGORIES), activation="softmax"),
        ]
    )
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


clf_data, clf_labels = load_all_class_frames(
    max_frames=MAX_FRAMES, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
)
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)

classifier = build_classifier()
print(classifier.summary())


classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=EPOCHS,
    batch_size=BATCH_SIZE,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{CNN_RESULTS_DIR}/best_model_CNN.h5"),
        saveLastModel(f"{CNN_RESULTS_DIR}/last_model_CNN.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, CNN_RESULTS_DIR)
