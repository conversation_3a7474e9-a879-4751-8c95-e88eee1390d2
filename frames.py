import os
import cv2
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import ndimage
from skimage import filters, exposure, morphology, feature
from config import IMG_SIZE, EQUALIZE, RESIZ<PERSON>


def preprocess_frame(frame, IMG_SIZE=IMG_SIZE, modes=[EQUALIZE, RESIZE]):
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    for mode in modes:
        if mode == EQUALIZE:
            frame = cv2.equalizeHist(frame)
        elif mode == RESIZE:
            frame = cv2.resize(frame, (IMG_SIZE, IMG_SIZE))
    return frame / 255.0


# ============================================================================
# ENHANCED PREPROCESSING TECHNIQUES
# ============================================================================

def adaptive_histogram_equalization(frame, clip_limit=2.0, tile_grid_size=(8, 8)):
    """
    Apply Contrast Limited Adaptive Histogram Equalization (CLAHE).

    Args:
        frame: Input grayscale frame
        clip_limit: Threshold for contrast limiting
        tile_grid_size: Size of the neighborhood for histogram equalization

    Returns:
        Enhanced frame
    """
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    return clahe.apply(frame)


def gamma_correction(frame, gamma=1.0):
    """
    Apply gamma correction to adjust brightness.

    Args:
        frame: Input frame (0-1 range)
        gamma: Gamma value (< 1 brightens, > 1 darkens)

    Returns:
        Gamma corrected frame
    """
    # Build lookup table
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")

    # Apply gamma correction
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_uint8 = (frame * 255).astype(np.uint8)
        corrected = cv2.LUT(frame_uint8, table)
        return corrected.astype(np.float32) / 255.0
    else:
        return cv2.LUT(frame, table)


def unsharp_masking(frame, kernel_size=(5, 5), sigma=1.0, amount=1.0, threshold=0):
    """
    Apply unsharp masking for edge enhancement.

    Args:
        frame: Input frame
        kernel_size: Size of Gaussian kernel
        sigma: Standard deviation for Gaussian kernel
        amount: Strength of sharpening
        threshold: Minimum difference required for sharpening

    Returns:
        Sharpened frame
    """
    # Create Gaussian blur
    blurred = cv2.GaussianBlur(frame, kernel_size, sigma)

    # Create mask
    mask = frame - blurred

    # Apply threshold
    if threshold > 0:
        mask = np.where(np.abs(mask) < threshold, 0, mask)

    # Apply unsharp mask
    sharpened = frame + amount * mask

    # Clip values to valid range
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        sharpened = np.clip(sharpened, 0.0, 1.0)
    else:
        sharpened = np.clip(sharpened, 0, 255).astype(frame.dtype)

    return sharpened


def bilateral_filter_denoising(frame, d=9, sigma_color=75, sigma_space=75):
    """
    Apply bilateral filtering for noise reduction while preserving edges.

    Args:
        frame: Input frame
        d: Diameter of pixel neighborhood
        sigma_color: Filter sigma in the color space
        sigma_space: Filter sigma in the coordinate space

    Returns:
        Denoised frame
    """
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_uint8 = (frame * 255).astype(np.uint8)
        filtered = cv2.bilateralFilter(frame_uint8, d, sigma_color, sigma_space)
        return filtered.astype(np.float32) / 255.0
    else:
        return cv2.bilateralFilter(frame, d, sigma_color, sigma_space)


def morphological_operations(frame, operation='opening', kernel_size=5, iterations=1):
    """
    Apply morphological operations for noise reduction and feature enhancement.

    Args:
        frame: Input binary or grayscale frame
        operation: 'opening', 'closing', 'gradient', 'tophat', 'blackhat'
        kernel_size: Size of morphological kernel
        iterations: Number of iterations

    Returns:
        Processed frame
    """
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))

    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_uint8 = (frame * 255).astype(np.uint8)
    else:
        frame_uint8 = frame

    if operation == 'opening':
        result = cv2.morphologyEx(frame_uint8, cv2.MORPH_OPEN, kernel, iterations=iterations)
    elif operation == 'closing':
        result = cv2.morphologyEx(frame_uint8, cv2.MORPH_CLOSE, kernel, iterations=iterations)
    elif operation == 'gradient':
        result = cv2.morphologyEx(frame_uint8, cv2.MORPH_GRADIENT, kernel, iterations=iterations)
    elif operation == 'tophat':
        result = cv2.morphologyEx(frame_uint8, cv2.MORPH_TOPHAT, kernel, iterations=iterations)
    elif operation == 'blackhat':
        result = cv2.morphologyEx(frame_uint8, cv2.MORPH_BLACKHAT, kernel, iterations=iterations)
    else:
        raise ValueError(f"Unknown morphological operation: {operation}")

    if frame.dtype == np.float32 or frame.dtype == np.float64:
        return result.astype(np.float32) / 255.0
    else:
        return result


def edge_enhancement(frame, method='sobel', **kwargs):
    """
    Apply edge enhancement techniques.

    Args:
        frame: Input frame
        method: 'sobel', 'canny', 'laplacian', 'scharr'
        **kwargs: Additional parameters for specific methods

    Returns:
        Edge-enhanced frame
    """
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_uint8 = (frame * 255).astype(np.uint8)
    else:
        frame_uint8 = frame

    if method == 'sobel':
        grad_x = cv2.Sobel(frame_uint8, cv2.CV_64F, 1, 0, ksize=kwargs.get('ksize', 3))
        grad_y = cv2.Sobel(frame_uint8, cv2.CV_64F, 0, 1, ksize=kwargs.get('ksize', 3))
        edges = np.sqrt(grad_x**2 + grad_y**2)
        edges = np.clip(edges, 0, 255).astype(np.uint8)

    elif method == 'canny':
        edges = cv2.Canny(
            frame_uint8,
            kwargs.get('low_threshold', 50),
            kwargs.get('high_threshold', 150),
            apertureSize=kwargs.get('aperture_size', 3)
        )

    elif method == 'laplacian':
        edges = cv2.Laplacian(frame_uint8, cv2.CV_64F, ksize=kwargs.get('ksize', 3))
        edges = np.absolute(edges)
        edges = np.clip(edges, 0, 255).astype(np.uint8)

    elif method == 'scharr':
        grad_x = cv2.Scharr(frame_uint8, cv2.CV_64F, 1, 0)
        grad_y = cv2.Scharr(frame_uint8, cv2.CV_64F, 0, 1)
        edges = np.sqrt(grad_x**2 + grad_y**2)
        edges = np.clip(edges, 0, 255).astype(np.uint8)

    else:
        raise ValueError(f"Unknown edge enhancement method: {method}")

    if frame.dtype == np.float32 or frame.dtype == np.float64:
        return edges.astype(np.float32) / 255.0
    else:
        return edges


def histogram_stretching(frame, percentile_low=2, percentile_high=98):
    """
    Apply histogram stretching for contrast enhancement.

    Args:
        frame: Input frame
        percentile_low: Lower percentile for stretching
        percentile_high: Upper percentile for stretching

    Returns:
        Contrast-enhanced frame
    """
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_work = (frame * 255).astype(np.uint8)
    else:
        frame_work = frame

    # Calculate percentiles
    p_low = np.percentile(frame_work, percentile_low)
    p_high = np.percentile(frame_work, percentile_high)

    # Stretch histogram
    stretched = np.clip((frame_work - p_low) * 255.0 / (p_high - p_low), 0, 255).astype(np.uint8)

    if frame.dtype == np.float32 or frame.dtype == np.float64:
        return stretched.astype(np.float32) / 255.0
    else:
        return stretched


def local_binary_pattern(frame, radius=3, n_points=24, method='uniform'):
    """
    Extract Local Binary Pattern features.

    Args:
        frame: Input grayscale frame
        radius: Radius of circle of sampling points
        n_points: Number of sampling points
        method: Method for LBP calculation

    Returns:
        LBP image
    """
    if frame.dtype == np.float32 or frame.dtype == np.float64:
        frame_uint8 = (frame * 255).astype(np.uint8)
    else:
        frame_uint8 = frame

    lbp = feature.local_binary_pattern(frame_uint8, n_points, radius, method=method)

    # Normalize to 0-255 range
    lbp_normalized = ((lbp - lbp.min()) / (lbp.max() - lbp.min()) * 255).astype(np.uint8)

    if frame.dtype == np.float32 or frame.dtype == np.float64:
        return lbp_normalized.astype(np.float32) / 255.0
    else:
        return lbp_normalized


def gaussian_pyramid(frame, levels=3):
    """
    Create Gaussian pyramid for multi-scale analysis.

    Args:
        frame: Input frame
        levels: Number of pyramid levels

    Returns:
        List of frames at different scales
    """
    pyramid = [frame]
    current = frame.copy()

    for i in range(levels - 1):
        current = cv2.pyrDown(current)
        pyramid.append(current)

    return pyramid


def laplacian_pyramid(frame, levels=3):
    """
    Create Laplacian pyramid for multi-scale edge analysis.

    Args:
        frame: Input frame
        levels: Number of pyramid levels

    Returns:
        List of Laplacian frames at different scales
    """
    gaussian_pyr = gaussian_pyramid(frame, levels + 1)
    laplacian_pyr = []

    for i in range(levels):
        # Expand the smaller image
        expanded = cv2.pyrUp(gaussian_pyr[i + 1])

        # Ensure same size (handle odd dimensions)
        if expanded.shape != gaussian_pyr[i].shape:
            expanded = cv2.resize(expanded, (gaussian_pyr[i].shape[1], gaussian_pyr[i].shape[0]))

        # Calculate Laplacian
        laplacian = cv2.subtract(gaussian_pyr[i], expanded)
        laplacian_pyr.append(laplacian)

    # Add the smallest Gaussian level
    laplacian_pyr.append(gaussian_pyr[-1])

    return laplacian_pyr


def advanced_preprocess_frame(frame, IMG_SIZE=IMG_SIZE, techniques=None):
    """
    Apply advanced preprocessing techniques to a frame.

    Args:
        frame: Input frame (BGR color)
        IMG_SIZE: Target size for resizing
        techniques: List of preprocessing techniques to apply

    Returns:
        Preprocessed frame
    """
    if techniques is None:
        techniques = ['grayscale', 'clahe', 'bilateral', 'resize', 'normalize']

    # Convert to grayscale
    if 'grayscale' in techniques:
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Apply CLAHE
    if 'clahe' in techniques:
        frame = adaptive_histogram_equalization(frame)

    # Apply histogram equalization
    if 'equalize' in techniques:
        frame = cv2.equalizeHist(frame)

    # Apply histogram stretching
    if 'stretch' in techniques:
        frame = histogram_stretching(frame)

    # Apply gamma correction
    if 'gamma' in techniques:
        frame = gamma_correction(frame, gamma=1.2)

    # Apply bilateral filtering
    if 'bilateral' in techniques:
        frame = bilateral_filter_denoising(frame)

    # Apply unsharp masking
    if 'sharpen' in techniques:
        frame = unsharp_masking(frame)

    # Apply morphological operations
    if 'morph_open' in techniques:
        frame = morphological_operations(frame, 'opening')
    if 'morph_close' in techniques:
        frame = morphological_operations(frame, 'closing')

    # Apply edge enhancement
    if 'edges' in techniques:
        edges = edge_enhancement(frame, 'sobel')
        # Combine original with edges
        frame = cv2.addWeighted(frame, 0.7, edges, 0.3, 0)

    # Resize
    if 'resize' in techniques:
        frame = cv2.resize(frame, (IMG_SIZE, IMG_SIZE))

    # Normalize
    if 'normalize' in techniques:
        if frame.dtype != np.float32 and frame.dtype != np.float64:
            frame = frame.astype(np.float32) / 255.0
        else:
            frame = np.clip(frame, 0.0, 1.0)

    return frame


def create_multi_channel_frame(frame, channels=['original', 'edges', 'lbp']):
    """
    Create multi-channel representation of a frame.

    Args:
        frame: Input grayscale frame
        channels: List of channels to create

    Returns:
        Multi-channel frame
    """
    channel_data = []

    for channel in channels:
        if channel == 'original':
            channel_data.append(frame)
        elif channel == 'edges':
            edges = edge_enhancement(frame, 'sobel')
            channel_data.append(edges)
        elif channel == 'lbp':
            lbp = local_binary_pattern(frame)
            channel_data.append(lbp)
        elif channel == 'laplacian':
            laplacian = edge_enhancement(frame, 'laplacian')
            channel_data.append(laplacian)
        elif channel == 'gradient':
            morph_grad = morphological_operations(frame, 'gradient')
            channel_data.append(morph_grad)

    # Stack channels
    if len(channel_data) == 1:
        return np.expand_dims(channel_data[0], -1)
    else:
        return np.stack(channel_data, axis=-1)


def upScaleFrame(frame, mode="bicubic", scale=2, out_size=None, **kwargs):
    """Upscale a single frame using different modes.

    Parameters
    - frame: np.ndarray HxW or HxWxC (BGR if from cv2)
    - mode: one of ['realesrgan','pyrup','lanczos','bicubic','nearest','pil_bicubic']
    - scale: integer upscaling factor (used by many modes)
    - out_size: (w,h) explicit output size (overrides scale)
    - kwargs: extra backend-specific args (e.g., model_path for opencv_dnn)

    Returns upscaled frame as np.ndarray in same channel layout as input.
    The function is defensive: if a requested backend isn't available it falls back
    to a robust OpenCV bicubic resize.
    """
    if frame is None:
        return None

    # Save original channel info
    orig_shape = frame.shape
    is_gray = (frame.ndim == 2) or (frame.shape[2] == 1)

    # Convert grayscale to 3-channel for some upscalers that expect color
    f = frame
    if is_gray:
        f = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    elif f.shape[2] == 4:
        # drop alpha
        f = cv2.cvtColor(f, cv2.COLOR_BGRA2BGR)

    h, w = f.shape[:2]
    if out_size is None:
        out_w, out_h = int(w * scale), int(h * scale)
    else:
        out_w, out_h = out_size

    mode = (mode or "bicubic").lower()

    # 1) Try Real-ESRGAN if requested and installed
    if mode == "realesrgan":
        try:
            from PIL import Image
            from realesrgan import RealESRGAN
            import torch

            device = "cuda" if torch.cuda.is_available() else "cpu"
            pil = Image.fromarray(cv2.cvtColor(f, cv2.COLOR_BGR2RGB))
            # RealESRGAN scale parameter may vary; try to create a model for given scale
            model = RealESRGAN(device, scale=scale)
            model.load_weights(kwargs.get("weights_name", None))
            out_pil = model.predict(pil)
            out = cv2.cvtColor(np.array(out_pil), cv2.COLOR_RGB2BGR)
            # convert back to gray if input was gray
            if is_gray:
                out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
            return out
        except Exception:
            # fallback to bicubic
            mode = "bicubic"

    # 2) OpenCV pyrUp (fast, decent)
    if mode == "pyrup":
        out = f.copy()
        for _ in range(int(np.log2(scale)) if scale > 1 else 1):
            out = cv2.pyrUp(out)
        out = cv2.resize(out, (out_w, out_h), interpolation=cv2.INTER_CUBIC)
        if is_gray:
            out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        return out

    # 3) Lanczos
    if mode == "lanczos" or mode == "lanczos4":
        out = cv2.resize(f, (out_w, out_h), interpolation=cv2.INTER_LANCZOS4)
        if is_gray:
            out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        return out

    # 4) Bicubic (opencv)
    if mode == "bicubic":
        out = cv2.resize(f, (out_w, out_h), interpolation=cv2.INTER_CUBIC)
        if is_gray:
            out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        return out

    # 5) Nearest
    if mode == "nearest":
        out = cv2.resize(f, (out_w, out_h), interpolation=cv2.INTER_NEAREST)
        if is_gray:
            out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        return out

    # 6) PIL bicubic (useful for some kernels)
    if mode == "pil_bicubic":
        try:
            from PIL import Image

            if is_gray:
                pil = Image.fromarray(frame)
            else:
                pil = Image.fromarray(cv2.cvtColor(f, cv2.COLOR_BGR2RGB))
            pil_out = pil.resize((out_w, out_h), resample=Image.BICUBIC)
            if is_gray:
                out = np.array(pil_out)
            else:
                out = cv2.cvtColor(np.array(pil_out), cv2.COLOR_RGB2BGR)
            return out
        except Exception:
            # fallback
            out = cv2.resize(f, (out_w, out_h), interpolation=cv2.INTER_CUBIC)
            if is_gray:
                out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
            return out

    # 7) Fallback: bicubic
    out = cv2.resize(f, (out_w, out_h), interpolation=cv2.INTER_CUBIC)
    if is_gray:
        out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
    return out


def group_frames_into_clips(frames, labels, num_frames):
    """Group frames into clips of num_frames for video classification."""
    clips = []
    clip_labels = []
    for i in range(0, len(frames) - num_frames + 1, num_frames):
        clip = frames[i : i + num_frames]
        if len(clip) == num_frames:
            clips.append(clip)
            clip_labels.append(labels[i])  # Use label of the first frame in the clip
    return np.array(clips), np.array(clip_labels)


# ============================================================================
# TEMPORAL PREPROCESSING TECHNIQUES
# ============================================================================

def temporal_difference(frames, method='adjacent'):
    """
    Compute temporal differences between frames.

    Args:
        frames: Array of frames (T, H, W) or (T, H, W, C)
        method: 'adjacent' or 'background_subtraction'

    Returns:
        Temporal difference frames
    """
    if method == 'adjacent':
        # Compute differences between adjacent frames
        diff_frames = []
        for i in range(1, len(frames)):
            diff = cv2.absdiff(frames[i-1], frames[i])
            diff_frames.append(diff)
        return np.array(diff_frames)

    elif method == 'background_subtraction':
        # Use first frame as background
        background = frames[0]
        diff_frames = []
        for i in range(1, len(frames)):
            diff = cv2.absdiff(background, frames[i])
            diff_frames.append(diff)
        return np.array(diff_frames)

    else:
        raise ValueError(f"Unknown temporal difference method: {method}")


def temporal_smoothing(frames, method='gaussian', **kwargs):
    """
    Apply temporal smoothing across frames.

    Args:
        frames: Array of frames (T, H, W) or (T, H, W, C)
        method: 'gaussian', 'median', 'mean'
        **kwargs: Additional parameters

    Returns:
        Temporally smoothed frames
    """
    if method == 'gaussian':
        sigma = kwargs.get('sigma', 1.0)
        smoothed = ndimage.gaussian_filter1d(frames, sigma=sigma, axis=0)
        return smoothed

    elif method == 'median':
        kernel_size = kwargs.get('kernel_size', 3)
        smoothed = ndimage.median_filter(frames, size=(kernel_size, 1, 1))
        return smoothed

    elif method == 'mean':
        kernel_size = kwargs.get('kernel_size', 3)
        smoothed = ndimage.uniform_filter1d(frames, size=kernel_size, axis=0)
        return smoothed

    else:
        raise ValueError(f"Unknown temporal smoothing method: {method}")


def extract_motion_history(frames, duration=5, threshold=30):
    """
    Create Motion History Image (MHI) from a sequence of frames.

    Args:
        frames: Array of frames (T, H, W)
        duration: Duration for motion history
        threshold: Threshold for motion detection

    Returns:
        Motion History Image
    """
    if len(frames) < 2:
        return frames[0] if len(frames) == 1 else None

    # Initialize MHI
    h, w = frames[0].shape[:2]
    mhi = np.zeros((h, w), dtype=np.float32)

    # Process frame pairs
    for i in range(1, len(frames)):
        # Convert frames to uint8 if needed
        frame1 = frames[i-1]
        frame2 = frames[i]

        if frame1.dtype != np.uint8:
            frame1 = (frame1 * 255).astype(np.uint8)
        if frame2.dtype != np.uint8:
            frame2 = (frame2 * 255).astype(np.uint8)

        # Compute frame difference
        diff = cv2.absdiff(frame1, frame2)

        # Threshold the difference
        _, motion_mask = cv2.threshold(diff, threshold, 1, cv2.THRESH_BINARY)

        # Update MHI
        cv2.updateMotionHistory(motion_mask, mhi, i, duration)

    # Normalize MHI
    mhi_normalized = cv2.normalize(mhi, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

    return mhi_normalized


def create_temporal_features(frames, feature_type='statistics'):
    """
    Extract temporal features from a sequence of frames.

    Args:
        frames: Array of frames (T, H, W) or (T, H, W, C)
        feature_type: 'statistics', 'gradients', 'energy'

    Returns:
        Temporal features
    """
    if feature_type == 'statistics':
        # Statistical features across time
        mean_frame = np.mean(frames, axis=0)
        std_frame = np.std(frames, axis=0)
        max_frame = np.max(frames, axis=0)
        min_frame = np.min(frames, axis=0)

        return {
            'mean': mean_frame,
            'std': std_frame,
            'max': max_frame,
            'min': min_frame
        }

    elif feature_type == 'gradients':
        # Temporal gradients
        gradients = np.gradient(frames, axis=0)
        return {
            'temporal_gradient': gradients,
            'gradient_magnitude': np.abs(gradients)
        }

    elif feature_type == 'energy':
        # Temporal energy
        diff_frames = temporal_difference(frames, 'adjacent')
        energy = np.sum(diff_frames ** 2, axis=0)
        return {
            'temporal_energy': energy
        }

    else:
        raise ValueError(f"Unknown temporal feature type: {feature_type}")


def adaptive_frame_sampling(frames, labels, target_frames, method='uniform'):
    """
    Adaptively sample frames from a video sequence.

    Args:
        frames: Array of frames (T, H, W) or (T, H, W, C)
        labels: Array of labels (T,)
        target_frames: Target number of frames
        method: 'uniform', 'motion_based', 'entropy_based'

    Returns:
        Sampled frames and labels
    """
    if len(frames) <= target_frames:
        return frames, labels

    if method == 'uniform':
        # Uniform sampling
        indices = np.linspace(0, len(frames) - 1, target_frames, dtype=int)
        return frames[indices], labels[indices]

    elif method == 'motion_based':
        # Sample based on motion content
        motion_scores = []
        for i in range(1, len(frames)):
            diff = cv2.absdiff(frames[i-1], frames[i])
            motion_score = np.sum(diff)
            motion_scores.append(motion_score)

        # Add first frame (no motion score)
        motion_scores = [0] + motion_scores
        motion_scores = np.array(motion_scores)

        # Select frames with highest motion
        indices = np.argsort(motion_scores)[-target_frames:]
        indices = np.sort(indices)  # Maintain temporal order

        return frames[indices], labels[indices]

    elif method == 'entropy_based':
        # Sample based on information content (entropy)
        entropy_scores = []
        for frame in frames:
            if frame.dtype != np.uint8:
                frame_uint8 = (frame * 255).astype(np.uint8)
            else:
                frame_uint8 = frame

            # Calculate histogram
            hist = cv2.calcHist([frame_uint8], [0], None, [256], [0, 256])
            hist = hist.flatten()
            hist = hist / hist.sum()  # Normalize

            # Calculate entropy
            entropy = -np.sum(hist * np.log2(hist + 1e-10))
            entropy_scores.append(entropy)

        entropy_scores = np.array(entropy_scores)

        # Select frames with highest entropy
        indices = np.argsort(entropy_scores)[-target_frames:]
        indices = np.sort(indices)  # Maintain temporal order

        return frames[indices], labels[indices]

    else:
        raise ValueError(f"Unknown sampling method: {method}")
