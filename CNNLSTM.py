import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.callbacks import ModelCheckpoint
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import pandas as pd
from helper import load_all_class_frames, evaluateModel, saveBestModel, saveLastModel
from config import (
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    NUM_FRAMES,
    CNN_LSTM_RESULTS_DIR,
)


def build_classifier():
    model = models.Sequential(
        [
            layers.Input(shape=(NUM_FRAMES, IMG_SIZE, IMG_SIZE, 1)),
            layers.TimeDistributed(
                layers.Conv2D(32, 3, activation="relu", padding="same")
            ),
            layers.TimeDistributed(layers.MaxPooling2D()),
            layers.TimeDistributed(
                layers.Conv2D(64, 3, activation="relu", padding="same")
            ),
            layers.TimeDistributed(layers.MaxPooling2D()),
            layers.TimeDistributed(layers.Flatten()),
            layers.LSTM(128, return_sequences=False),
            layers.Dropout(0.3),
            layers.Dense(len(CATEGORIES), activation="softmax"),
        ]
    )
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# Note: For CNN+LSTM, data needs to be sequences of frames.
# Assuming load_all_class_frames loads single frames, we need to group them into sequences.
# This is a placeholder; you may need to modify data loading for sequences.
clf_data, clf_labels = load_all_class_frames(
    max_frames=1000, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
)
# Placeholder: Reshape to sequences (this assumes frames are sequential per video)
# In practice, group frames by video and create sequences of NUM_FRAMES.
# For simplicity, assuming clf_data is already shaped as (num_samples, NUM_FRAMES, IMG_SIZE, IMG_SIZE, 1)
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)

classifier = build_classifier()
print(classifier.summary())

os.makedirs(CNN_LSTM_RESULTS_DIR, exist_ok=True)


classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=10,
    batch_size=32,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{CNN_LSTM_RESULTS_DIR}/best_model_CNN_LSTM.h5"),
        saveLastModel(f"{CNN_LSTM_RESULTS_DIR}/last_model_CNN_LSTM.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, CATEGORIES, CNN_LSTM_RESULTS_DIR)
