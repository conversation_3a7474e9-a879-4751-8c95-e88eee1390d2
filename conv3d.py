import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import pandas as pd
from helper import load_all_class_frames, evaluateModel, saveBestModel, saveLastModel
from config import (
    CNN3D_RESULTS_DIR,
    DATA_PATH,
    SKIP,
    CATEGORIES,
    IMG_SIZE,
    NUM_FRAMES,
    MAX_FRAMES,
    EPOCHS,
    BATCH_SIZE,
)
from frames import group_frames_into_clips


def build_classifier():
    model = models.Sequential(
        [
            layers.Input(shape=(NUM_FRAMES, IMG_SIZE, IMG_SIZE, 1)),
            layers.Conv3D(32, (3, 3, 3), activation="relu", padding="same"),
            layers.MaxPooling3D(pool_size=(2, 2, 2)),  # Single pooling layer
            layers.Conv3D(64, (3, 3, 3), activation="relu", padding="same"),
            layers.Flatten(),
            layers.Dense(128, activation="relu"),
            layers.Dropout(0.3),
            layers.Dense(len(CATEGORIES), activation="softmax"),
        ]
    )
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# Load video data
clf_data, clf_labels = load_all_class_frames(
    max_frames=MAX_FRAMES, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
)

# Debug: Print data shapes
print(f"clf_data shape: {clf_data.shape}")
print(f"clf_labels shape: {clf_labels.shape}")

# Group frames into clips
clf_data, clf_labels = group_frames_into_clips(clf_data, clf_labels, NUM_FRAMES)
print(f"Clipped clf_data shape: {clf_data.shape}")
print(f"Clipped clf_labels shape: {clf_labels.shape}")

# Split data
x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
    clf_data, clf_labels, test_size=0.2, random_state=42
)
print(f"x_clf_train shape: {x_clf_train.shape}")
print(f"x_clf_test shape: {x_clf_test.shape}")

classifier = build_classifier()
print(classifier.summary())

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=EPOCHS,
    batch_size=BATCH_SIZE,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[
        saveBestModel(f"{CNN3D_RESULTS_DIR}/best_model_conv3d.h5"),
        saveLastModel(f"{CNN3D_RESULTS_DIR}/last_model_conv3d.h5"),
    ],
)

evaluateModel(classifier, x_clf_test, y_clf_test, CNN3D_RESULTS_DIR)
