import os

IMG_SIZE = 64
DATA_PATH = "/kaggle/input/real-time-anomaly-detection-in-cctv-surveillance/data"
CATEGORIES = ["Arrest", "Attack", "Burglary", "Explosion", "Fighting"]
CATEGORIES = ["abuse", "arrest", "arson", "assault"]


def getCategories(DATA_PATH=DATA_PATH, full=False):
    if not full:
        return CATEGORIES
    return [
        d for d in os.listdir(DATA_PATH) if os.path.isdir(os.path.join(DATA_PATH, d))
    ]


# CATEGORIES = getCategories(full=True)
SKIP = 10
CNN_RESULTS_DIR = "Results/CNN"
CNN3D_RESULTS_DIR = "Results/Conv3D"
VIT_RESULTS_DIR = "Results/VIT"
NUM_FRAMES = 4


import os
import cv2
import numpy as np


# Step 2: Frame Preprocessing
def preprocess_frame(frame, IMG_SIZE=IMG_SIZE):
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    frame = cv2.resize(frame, (IMG_SIZE, IMG_SIZE))
    frame = cv2.equalizeHist(frame)
    return frame / 255.0


# Step 3: Extract frames from videos
def extract_frames_from_videos(folder, max_frames_per_video=10, skip=20):
    data = []

    # Basic input validation
    if not os.path.exists(folder):
        print(f"Folder not found: {folder}")
        return np.array(data)

    if max_frames_per_video <= 0:
        print("max_frames_per_video must be > 0")
        return np.array(data)

    if skip is None or skip < 0:
        print("Invalid skip value provided, using default skip=10")
        skip = 10

    try:
        files = os.listdir(folder)
    except Exception as e:
        print(f"Could not list folder {folder}: {e}")
        return np.array(data)

    print(f"Loading from {folder} ({len(files)} files)")

    for file in files:
        if not file.lower().endswith((".mp4", ".avi", ".mov", ".mkv")):
            continue
        cap = cv2.VideoCapture(os.path.join(folder, file))
        count = 0  # number of frames extracted for this video
        frame_idx = 0  # absolute frame index read from video
        while cap.isOpened() and (
            count < max_frames_per_video or max_frames_per_video == -1
        ):
            ret, frame = cap.read()
            if not ret:
                break

            # Take the frame if it's the sampling frame (every skip+1 frames)
            if frame_idx % (skip + 1) == 0:
                processed = preprocess_frame(frame)
                if processed is None:
                    frame_idx += 1
                    continue
                data.append(np.expand_dims(processed, -1))  # (H, W, 1)
                count += 1

            frame_idx += 1

        cap.release()

    print(f"Extracted {len(data)} frames from {folder}")
    return np.array(data)


def load_all_class_frames(
    max_frames=5, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=20
):
    all_data = []
    all_labels = []
    label_map = {cat: idx for idx, cat in enumerate(CATEGORIES)}

    if CATEGORIES is None or len(CATEGORIES) == 0:
        # getting all the classes on the Data Path
        CATEGORIES = [
            d
            for d in os.listdir(DATA_PATH)
            if os.path.isdir(os.path.join(DATA_PATH, d))
        ]
        print(f"Detected categories: {CATEGORIES}")

    for cat in CATEGORIES:
        print(f"{cat}")
        folder = os.path.join(DATA_PATH, cat)
        frames = extract_frames_from_videos(
            folder, max_frames_per_video=max_frames, skip=skip
        )
        all_data.extend(frames)
        all_labels.extend([label_map[cat]] * len(frames))

    return np.array(all_data), np.array(all_labels)


import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.callbacks import ModelCheckpoint
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import pandas as pd


def build_classifier():
    model = models.Sequential(
        [
            layers.Input(shape=(IMG_SIZE, IMG_SIZE, 1)),
            layers.Conv2D(32, 3, activation="relu", padding="same"),
            layers.MaxPooling2D(),
            layers.Conv2D(64, 3, activation="relu", padding="same"),
            layers.MaxPooling2D(),
            layers.Flatten(),
            layers.Dense(128, activation="relu"),
            layers.Dropout(0.3),
            layers.Dense(len(CATEGORIES), activation="softmax"),
        ]
    )
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


# CATEGORIES = getCategories(DATA_PATH, True)

# clf_data, clf_labels = load_all_class_frames(
#     max_frames=2500, CATEGORIES=CATEGORIES, DATA_PATH=DATA_PATH, skip=SKIP
# )
# x_clf_train, x_clf_test, y_clf_train, y_clf_test = train_test_split(
#     clf_data, clf_labels, test_size=0.2, random_state=42
# )

# classifier = build_classifier()
print(classifier.summary())

os.makedirs(CNN_RESULTS_DIR, exist_ok=True)

# Callbacks to save best and last models
best_checkpoint = ModelCheckpoint(
    filepath=f"{CNN_RESULTS_DIR}/best_model.h5",
    monitor="val_accuracy",
    mode="max",
    save_best_only=True,
    verbose=1,
)

last_checkpoint = ModelCheckpoint(
    filepath=f"{CNN_RESULTS_DIR}/last_model.h5", save_freq="epoch", verbose=1
)

classifier.fit(
    x_clf_train,
    y_clf_train,
    epochs=10,
    batch_size=32,
    validation_data=(x_clf_test, y_clf_test),
    callbacks=[best_checkpoint, last_checkpoint],
)

y_pred = np.argmax(classifier.predict(x_clf_test), axis=1)
print(classification_report(y_clf_test, y_pred, target_names=CATEGORIES))


# save confusion matrix
cm = confusion_matrix(y_clf_test, y_pred)
plt.figure(figsize=(10, 8))
plt.imshow(cm, interpolation="nearest", cmap=plt.cm.Blues)
plt.title("Confusion Matrix")
plt.colorbar()
plt.savefig(f"{CNN_RESULTS_DIR}/confusion_matrix.png")

# save classification report
report = classification_report(
    y_clf_test, y_pred, target_names=CATEGORIES, output_dict=True
)

df = pd.DataFrame(report).transpose()
df.to_csv(f"{CNN_RESULTS_DIR}/classification_report.csv")


# evaluate the model
loss, accuracy = classifier.evaluate(x_clf_test, y_clf_test)
print(f"Test Loss: {loss:.4f}")
print(f"Test Accuracy: {accuracy:.4f}")
